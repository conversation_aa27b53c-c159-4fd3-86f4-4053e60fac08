"""
简化的本地记忆模块
直连PostgreSQL，适配现有Memobase表结构
"""

import os
import json
import psycopg2
import psycopg2.extras
from typing import Optional, Dict, List
from contextlib import contextmanager

# PostgreSQL数据库连接配置
DATABASE_URL = os.getenv('MEMORY_DATABASE_URL', 'postgresql://memobase:memobase123@localhost:5433/memobase')

# 固定的项目ID，用于我们的虚拟人应用
PROJECT_ID = "virtual_companion_app"


class MemoryDatabase:
    """简化的记忆数据库管理器"""
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or DATABASE_URL
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = psycopg2.connect(self.database_url)
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """数据库初始化，确保项目和必要表存在"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 确保项目存在（如果projects表存在的话）
                try:
                    cursor.execute('''
                        INSERT INTO projects (project_id, project_secret, status, id, created_at, updated_at)
                        VALUES (%s, %s, %s, gen_random_uuid(), NOW(), NOW())
                        ON CONFLICT (project_id) DO NOTHING
                    ''', (PROJECT_ID, 'secret123', 'active'))
                    conn.commit()
                    print(f"✅ 项目 {PROJECT_ID} 已确保存在")
                except Exception as e:
                    print(f"⚠️ 项目初始化跳过: {e}")

                # 创建必要的表
                self._ensure_table_exists(cursor, 'conversations')
                self._ensure_table_exists(cursor, 'affection_levels')
                self._ensure_table_exists(cursor, 'persona_states')
                conn.commit()
                print("✅ 必要表已创建")

        except Exception as e:
            print(f"⚠️ 数据库初始化失败: {e}")

    def _ensure_table_exists(self, cursor, table_name: str):
        """按需创建表"""
        if table_name == 'conversations':
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id SERIAL PRIMARY KEY,
                    user_id UUID NOT NULL,
                    project_id VARCHAR(64) NOT NULL DEFAULT %s,
                    message TEXT NOT NULL,
                    sender VARCHAR(20) NOT NULL,
                    emotion_score FLOAT DEFAULT 0.0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''', (PROJECT_ID,))
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id, project_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_timestamp ON conversations(timestamp)')

        elif table_name == 'affection_levels':
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS affection_levels (
                    id SERIAL PRIMARY KEY,
                    user_id UUID NOT NULL,
                    project_id VARCHAR(64) NOT NULL DEFAULT %s,
                    level INTEGER NOT NULL,
                    change_reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''', (PROJECT_ID,))
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_affection_user_id ON affection_levels(user_id, project_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_affection_timestamp ON affection_levels(timestamp)')

        elif table_name == 'persona_states':
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS persona_states (
                    id SERIAL PRIMARY KEY,
                    project_id VARCHAR(64) NOT NULL DEFAULT %s,
                    date DATE NOT NULL,
                    current_activity TEXT,
                    mood TEXT,
                    work_content TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''', (PROJECT_ID,))
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_persona_states_date ON persona_states(date, project_id)')



    def _get_user_uuid(self, cursor, user_id: str) -> Optional[str]:
        """获取用户的UUID（内部辅助方法）"""
        cursor.execute('''
            SELECT id FROM users
            WHERE project_id = %s
            AND additional_fields->>'user_id' = %s
        ''', (PROJECT_ID, user_id))
        result = cursor.fetchone()
        return str(result[0]) if result else None

    def create_user(self, user_id: str, name: str = None, additional_data: Dict = None) -> Dict:
        """创建或获取用户（适配现有Memobase表结构）"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 尝试获取现有用户（通过additional_fields中的user_id查找）
            cursor.execute('''
                SELECT * FROM users
                WHERE project_id = %s
                AND additional_fields->>'user_id' = %s
            ''', (PROJECT_ID, user_id))
            user = cursor.fetchone()

            if user:
                return dict(user)

            # 创建新用户
            additional_fields = additional_data or {}
            additional_fields['user_id'] = user_id
            additional_fields['name'] = name or f'用户{user_id[:8]}'

            cursor.execute('''
                INSERT INTO users (id, project_id, additional_fields)
                VALUES (gen_random_uuid(), %s, %s)
                RETURNING *
            ''', (PROJECT_ID, json.dumps(additional_fields)))

            user = cursor.fetchone()
            conn.commit()
            return dict(user)
    
    def add_user_event(self, user_id: str, event_data: Dict, embedding: List[float] = None) -> str:
        """添加用户事件（适配现有Memobase表结构）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                raise ValueError(f"用户 {user_id} 不存在")

            cursor.execute('''
                INSERT INTO user_events (user_id, project_id, event_data, embedding)
                VALUES (%s, %s, %s, %s)
                RETURNING id
            ''', (user_uuid, PROJECT_ID, json.dumps(event_data), embedding))

            event_id = cursor.fetchone()[0]
            conn.commit()
            return str(event_id)

    def get_user_events(self, user_id: str, limit: int = 10) -> List[Dict]:
        """获取用户事件（适配现有表结构）"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                return []

            cursor.execute('''
                SELECT * FROM user_events
                WHERE user_id = %s AND project_id = %s
                ORDER BY created_at DESC LIMIT %s
            ''', (user_uuid, PROJECT_ID, limit))

            return [dict(row) for row in cursor.fetchall()]

    def search_similar_events(self, user_id: str, query_embedding: List[float],
                             limit: int = 5, threshold: float = 0.7) -> List[Dict]:
        """基于向量相似度搜索事件（适配现有表结构）"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                return []

            # 使用pgvector的余弦相似度搜索
            cursor.execute('''
                SELECT *,
                       1 - (embedding <=> %s::vector) as similarity
                FROM user_events
                WHERE user_id = %s AND project_id = %s
                  AND embedding IS NOT NULL
                  AND 1 - (embedding <=> %s::vector) > %s
                ORDER BY embedding <=> %s::vector
                LIMIT %s
            ''', (query_embedding, user_uuid, PROJECT_ID, query_embedding, threshold, query_embedding, limit))

            return [dict(row) for row in cursor.fetchall()]
    
    def add_user_profile(self, user_id: str, content: str, attributes: Dict = None,
                        embedding: List[float] = None) -> str:
        """添加用户画像（适配现有表结构）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                raise ValueError(f"用户 {user_id} 不存在")

            cursor.execute('''
                INSERT INTO user_profiles (user_id, project_id, content, attributes, embedding)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING id
            ''', (user_uuid, PROJECT_ID, content, json.dumps(attributes or {}), embedding))

            profile_id = cursor.fetchone()[0]
            conn.commit()
            return str(profile_id)

    def get_user_profiles(self, user_id: str, topic: str = None, limit: int = 20) -> List[Dict]:
        """获取用户画像（适配现有表结构）"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                return []

            if topic:
                cursor.execute('''
                    SELECT * FROM user_profiles
                    WHERE user_id = %s AND project_id = %s
                      AND attributes->>'topic' = %s
                    ORDER BY updated_at DESC LIMIT %s
                ''', (user_uuid, PROJECT_ID, topic, limit))
            else:
                cursor.execute('''
                    SELECT * FROM user_profiles
                    WHERE user_id = %s AND project_id = %s
                    ORDER BY updated_at DESC LIMIT %s
                ''', (user_uuid, PROJECT_ID, limit))

            return [dict(row) for row in cursor.fetchall()]

    def search_similar_profiles(self, user_id: str, query_embedding: List[float],
                               limit: int = 5, threshold: float = 0.7) -> List[Dict]:
        """基于向量相似度搜索用户画像（适配现有表结构）"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                return []

            cursor.execute('''
                SELECT *,
                       1 - (embedding <=> %s::vector) as similarity
                FROM user_profiles
                WHERE user_id = %s AND project_id = %s
                  AND embedding IS NOT NULL
                  AND 1 - (embedding <=> %s::vector) > %s
                ORDER BY embedding <=> %s::vector
                LIMIT %s
            ''', (query_embedding, user_uuid, PROJECT_ID, query_embedding, threshold, query_embedding, limit))

            return [dict(row) for row in cursor.fetchall()]
    
    def save_conversation(self, user_id: str, message: str, sender: str, emotion_score: float = 0.0):
        """保存对话记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                # 如果用户不存在，先创建用户
                self.create_user(user_id)
                user_uuid = self._get_user_uuid(cursor, user_id)

            cursor.execute('''
                INSERT INTO conversations (user_id, project_id, message, sender, emotion_score)
                VALUES (%s, %s, %s, %s, %s)
            ''', (user_uuid, PROJECT_ID, message, sender, emotion_score))

            conn.commit()
    
    def get_recent_conversations(self, user_id: str, limit: int = 10) -> List[Dict]:
        """获取最近对话"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                return []

            cursor.execute('''
                SELECT * FROM conversations
                WHERE user_id = %s AND project_id = %s
                ORDER BY timestamp DESC LIMIT %s
            ''', (user_uuid, PROJECT_ID, limit))

            return [dict(row) for row in cursor.fetchall()]
    
    def update_affection(self, user_id: str, change: int, reason: str) -> int:
        """更新好感度"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                # 如果用户不存在，先创建用户
                self.create_user(user_id)
                user_uuid = self._get_user_uuid(cursor, user_id)

            # 获取当前好感度
            cursor.execute('''
                SELECT level FROM affection_levels
                WHERE user_id = %s AND project_id = %s
                ORDER BY timestamp DESC LIMIT 1
            ''', (user_uuid, PROJECT_ID))

            result = cursor.fetchone()
            current_level = result[0] if result else 10  # 默认初始好感度

            # 计算新好感度
            new_level = max(0, min(100, current_level + change))

            # 记录好感度变化
            cursor.execute('''
                INSERT INTO affection_levels (user_id, project_id, level, change_reason)
                VALUES (%s, %s, %s, %s)
            ''', (user_uuid, PROJECT_ID, new_level, reason))

            conn.commit()
            return new_level
    
    def get_current_affection(self, user_id: str) -> int:
        """获取当前好感度"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = self._get_user_uuid(cursor, user_id)
            if not user_uuid:
                return 10  # 默认初始好感度

            cursor.execute('''
                SELECT level FROM affection_levels
                WHERE user_id = %s AND project_id = %s
                ORDER BY timestamp DESC LIMIT 1
            ''', (user_uuid, PROJECT_ID))

            result = cursor.fetchone()
            return result[0] if result else 10  # 默认初始好感度


# 全局数据库实例
memory_db = None

def get_memory_db() -> MemoryDatabase:
    """获取记忆数据库实例"""
    global memory_db
    if memory_db is None:
        memory_db = MemoryDatabase()
    return memory_db
