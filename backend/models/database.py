"""
此文件已废弃！
所有数据库功能已迁移到PostgreSQL。
请使用 models/memory.py 中的 MemoryDatabase 类。
"""

class DatabaseManager:
    """废弃的SQLite数据库管理器"""
    
    def __init__(self):
        raise DeprecationWarning(
            "DatabaseManager已废弃！请使用 models.memory.get_memory_db() 获取PostgreSQL数据库实例。"
        )
    
    def __getattr__(self, name):
        raise DeprecationWarning(
            f"DatabaseManager.{name}已废弃！请使用 models.memory.get_memory_db() 获取PostgreSQL数据库实例。"
        )
