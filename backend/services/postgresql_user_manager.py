"""
PostgreSQL用户管理模块
提供用户注册、登录、管理等功能
"""

import hashlib
import secrets
import re
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional
from models.memory import get_memory_db

# 配置日志
logger = logging.getLogger(__name__)

class PostgreSQLUserManager:
    """PostgreSQL用户管理器"""
    
    def __init__(self):
        """初始化用户管理器"""
        self.memory_db = get_memory_db()
        # 确保数据库初始化
        self.memory_db.init_database()
        
        # 会话过期时间（7天）
        self.session_expire_days = 7
        
        # 创建用户相关表
        self._ensure_user_tables()
    
    def _ensure_user_tables(self):
        """确保用户相关表存在"""
        try:
            with self.memory_db.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建用户认证表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_auth (
                        id SERIAL PRIMARY KEY,
                        user_id UUID NOT NULL,
                        project_id VARCHAR(64) NOT NULL DEFAULT %s,
                        username VARCHAR(50) UNIQUE NOT NULL,
                        password_hash TEXT,
                        email VARCHAR(100),
                        status VARCHAR(20) DEFAULT 'active',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''', ("virtual_companion_app",))
                
                # 创建用户会话表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_sessions (
                        id SERIAL PRIMARY KEY,
                        session_token VARCHAR(64) UNIQUE NOT NULL,
                        user_id UUID NOT NULL,
                        project_id VARCHAR(64) NOT NULL DEFAULT %s,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NOT NULL,
                        is_active BOOLEAN DEFAULT TRUE
                    )
                ''', ("virtual_companion_app",))
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_auth_username ON user_auth(username)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_auth_user_id ON user_auth(user_id, project_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id, project_id)')
                
                conn.commit()
                logger.info("✅ 用户认证表已创建")
                
        except Exception as e:
            logger.error(f"💥 创建用户表失败: {e}")
    
    def create_user(self, username: str, password: str = None, nickname: str = None, 
                   email: str = None) -> Dict:
        """创建新用户"""
        try:
            # 验证用户名
            if not self._validate_username(username):
                raise ValueError("用户名格式不正确（3-20个字符，只允许字母、数字、下划线）")
            
            # 检查用户名是否已存在
            if self._username_exists(username):
                raise ValueError("用户名已存在")
            
            # 生成用户ID
            user_id = str(uuid.uuid4())
            
            # 处理密码
            password_hash = None
            if password:
                if not self._validate_password(password):
                    raise ValueError("密码格式不正确（6-50个字符）")
                password_hash = self._hash_password(password)
            
            # 验证邮箱
            if email and not self._validate_email(email):
                raise ValueError("邮箱格式不正确")
            
            with self.memory_db.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建用户认证记录
                cursor.execute('''
                    INSERT INTO user_auth (user_id, project_id, username, password_hash, email, status)
                    VALUES (%s, %s, %s, %s, %s, %s)
                ''', (user_id, "virtual_companion_app", username, password_hash, email, 'active'))
                
                # 创建Memobase用户记录
                self.memory_db.create_user(username, name=nickname or username)
                
                conn.commit()
            
            logger.info(f"✅ 用户创建成功 - 用户名: {username}, ID: {user_id}")
            
            return {
                'user_id': user_id,
                'username': username,
                'nickname': nickname or username,
                'email': email,
                'status': 'active',
                'created_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"💥 用户创建失败 - 用户名: {username}, 错误: {str(e)}")
            raise
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """用户认证"""
        try:
            with self.memory_db.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT user_id, username, password_hash, email, status, created_at
                    FROM user_auth
                    WHERE username = %s AND project_id = %s
                ''', (username, "virtual_companion_app"))
                
                user = cursor.fetchone()
                if not user:
                    logger.warning(f"⚠️  认证失败 - 用户不存在: {username}")
                    return None
                
                user_id, username, password_hash, email, status, created_at = user
                
                # 检查用户状态
                if status != 'active':
                    logger.warning(f"⚠️  认证失败 - 用户状态异常: {username}, 状态: {status}")
                    return None
                
                # 验证密码
                if not password_hash:
                    logger.warning(f"⚠️  认证失败 - 用户未设置密码: {username}")
                    return None
                
                if not self._verify_password(password, password_hash):
                    logger.warning(f"⚠️  认证失败 - 密码错误: {username}")
                    return None
                
                logger.info(f"✅ 用户认证成功 - 用户名: {username}")
                
                return {
                    'user_id': str(user_id),
                    'username': username,
                    'nickname': username,  # 暂时使用username作为nickname
                    'email': email,
                    'status': status,
                    'created_at': created_at.isoformat() if created_at else None
                }
                
        except Exception as e:
            logger.error(f"💥 用户认证异常 - 用户名: {username}, 错误: {str(e)}")
            return None
    
    def create_session(self, user_id: str) -> str:
        """创建用户会话"""
        try:
            # 生成会话令牌
            session_token = self._generate_session_token()
            
            # 计算过期时间
            expires_at = datetime.now() + timedelta(days=self.session_expire_days)
            
            with self.memory_db.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO user_sessions (session_token, user_id, project_id, expires_at)
                    VALUES (%s, %s, %s, %s)
                ''', (session_token, user_id, "virtual_companion_app", expires_at))
                
                conn.commit()
            
            logger.info(f"✅ 会话创建成功 - 用户ID: {user_id}, 令牌: {session_token[:8]}...")
            
            return session_token
            
        except Exception as e:
            logger.error(f"💥 会话创建失败 - 用户ID: {user_id}, 错误: {str(e)}")
            raise
    
    def validate_session(self, session_token: str) -> Optional[Dict]:
        """验证会话"""
        try:
            with self.memory_db.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT s.user_id, s.expires_at, s.is_active, u.username, u.email, u.status
                    FROM user_sessions s
                    JOIN user_auth u ON s.user_id = u.user_id AND s.project_id = u.project_id
                    WHERE s.session_token = %s AND s.project_id = %s
                ''', (session_token, "virtual_companion_app"))
                
                result = cursor.fetchone()
                if not result:
                    return None
                
                user_id, expires_at, is_active, username, email, status = result
                
                # 检查会话是否过期
                if datetime.now() > expires_at:
                    logger.info(f"⚠️  会话已过期 - 令牌: {session_token[:8]}...")
                    return None
                
                # 检查会话是否活跃
                if not is_active:
                    logger.info(f"⚠️  会话已失效 - 令牌: {session_token[:8]}...")
                    return None
                
                # 检查用户状态
                if status != 'active':
                    logger.warning(f"⚠️  会话对应用户状态异常 - 用户ID: {user_id}")
                    return None
                
                return {
                    'user_id': str(user_id),
                    'username': username,
                    'nickname': username,
                    'email': email,
                    'status': status
                }
                
        except Exception as e:
            logger.error(f"💥 会话验证异常 - 令牌: {session_token[:8]}..., 错误: {str(e)}")
            return None
    
    def logout_user(self, session_token: str) -> bool:
        """用户登出"""
        try:
            with self.memory_db.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE user_sessions
                    SET is_active = FALSE
                    WHERE session_token = %s AND project_id = %s
                ''', (session_token, "virtual_companion_app"))
                
                conn.commit()
                result = cursor.rowcount > 0
                
                if result:
                    logger.info(f"✅ 用户登出成功 - 令牌: {session_token[:8]}...")
                return result
        except Exception as e:
            logger.error(f"💥 用户登出失败 - 令牌: {session_token[:8]}..., 错误: {str(e)}")
            return False
    
    def _generate_session_token(self) -> str:
        """生成会话令牌"""
        return secrets.token_urlsafe(32)
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, hash_value = password_hash.split(':')
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return computed_hash.hex() == hash_value
        except:
            return False
    
    def _validate_username(self, username: str) -> bool:
        """验证用户名格式"""
        if not username or len(username) < 3 or len(username) > 20:
            return False
        return re.match(r'^[a-zA-Z0-9_]+$', username) is not None
    
    def _validate_password(self, password: str) -> bool:
        """验证密码格式"""
        if not password or len(password) < 6 or len(password) > 50:
            return False
        return True
    
    def _validate_email(self, email: str) -> bool:
        """验证邮箱格式"""
        if not email:
            return True
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def _username_exists(self, username: str) -> bool:
        """检查用户名是否已存在"""
        try:
            with self.memory_db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT 1 FROM user_auth
                    WHERE username = %s AND project_id = %s
                ''', (username, "virtual_companion_app"))
                return cursor.fetchone() is not None
        except:
            return False


# 全局实例
postgresql_user_manager = None

def get_postgresql_user_manager() -> PostgreSQLUserManager:
    """获取PostgreSQL用户管理器实例"""
    global postgresql_user_manager
    if postgresql_user_manager is None:
        postgresql_user_manager = PostgreSQLUserManager()
    return postgresql_user_manager
